<html xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers" data-namespace-typo3-fluid="true">

<!-- FlightFromTo Plugin Template -->
<f:if condition="{hasAnyList}">
    <f:then>
        <div class="active-offers__wrapper">
            <div class="active-offers__title"></div>
            <div class="active-offers__content">
                <f:if condition="{hasFromList}">
                    <div class="tp-card-container offer-card-2x2">
                        <div class="active-offers__title">
                            <h2>
                                <f:if condition="{fromTitle}">
                                    <f:then>{fromTitle}</f:then>
                                    <f:else>Еднопосочни от [_destination_name_]</f:else>
                                </f:if>
                            </h2>
                        </div>
                        <div class="mdc-card tp-offer-card">
                        <f:for each="{fromList}" as="item">
                            <a class='tp'
                               data-type="flight-link"
                               data-title="<f:if condition="{item.processedItem}"><f:then>{item.processedItem}</f:then><f:else><f:if condition="{fromItem}"><f:then>{fromItem}</f:then><f:else>{item.name}</f:else></f:if></f:else></f:if>"
                               data-endpoint="<f:if condition="{item.processedEndpoint}"><f:then>{item.processedEndpoint}</f:then><f:else><f:if condition="{fromEndpoint}"><f:then>{fromEndpoint}</f:then><f:else></f:else></f:if></f:else></f:if>"
                               href="<f:if condition="{item.processedHref}"><f:then>{item.processedHref}</f:then><f:else><f:if condition="{fromHref}"><f:then>{fromHref}</f:then><f:else></f:else></f:if></f:else></f:if>">[_destination_name_] - {item.name}</a>
                        </f:for>
                        </div>
                    </div>
                </f:if>

                <f:if condition="{hasToList}">
                    <div class="tp-card-container offer-card-2x2">
                        <div class="active-offers__title">
                            <h2>
                                <f:if condition="{toTitle}">
                                    <f:then>{toTitle}</f:then>
                                    <f:else>Еднопосочни до [_destination_name_]</f:else>
                                </f:if>
                            </h2>
                        </div>
                        <div class="mdc-card tp-offer-card">
                        <f:for each="{toList}" as="item">
                            <a class='tp'
                               data-type="flight-link"
                               data-title="<f:if condition="{item.processedItem}"><f:then>{item.processedItem}</f:then><f:else><f:if condition="{toItem}"><f:then>{toItem}</f:then><f:else>{item.name}</f:else></f:if></f:else></f:if>"
                               data-endpoint="<f:if condition="{item.processedEndpoint}"><f:then>{item.processedEndpoint}</f:then><f:else><f:if condition="{toEndpoint}"><f:then>{toEndpoint}</f:then><f:else></f:else></f:if></f:else></f:if>"
                               href="<f:if condition="{item.processedHref}"><f:then>{item.processedHref}</f:then><f:else><f:if condition="{toHref}"><f:then>{toHref}</f:then><f:else></f:else></f:if></f:else></f:if>">{item.name} - [_destination_name_]</a>
                        </f:for>
                        </div>
                    </div>
                </f:if>
            </div>
        </div>
    </f:then>
</f:if>

</html>
