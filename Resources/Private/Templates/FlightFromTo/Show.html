<html xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers" data-namespace-typo3-fluid="true">

<!-- FlightFromTo Plugin Template -->
<f:if condition="{hasAnyList}">
    <f:then>
        <div class="active-offers__wrapper">
            <div class="active-offers__title"></div>
            <div class="active-offers__content">

        <div class="flight-from-to-container">
            <f:if condition="{hasFromList}">
                <div class="tp-card-container offer-card-2x2">
                    <div class="active-offers__title">
                        <h2>
                            <f:if condition="{fromTitle}">
                                <f:then>{fromTitle}</f:then>
                                <f:else>Еднопосочни от [_destination_name_]</f:else>
                            </f:if>
                        </h2>
                    </div>
                    <div class="mdc-card tp-offer-card">
                    <f:for each="{fromList}" as="item">
                        <a class='tp'
                           data-type="flight-link"
                           data-title="{f:if(condition: fromItem, then: fromItem, else: 'До {item.name}')}"
                           data-endpoint="{f:if(condition: fromEndpoint, then: fromEndpoint, else: 'https://wizatour.fiestatravel.bg/wt/bg_BG/fieFlights/Search/hotels')}"
                           href="{f:if(condition: fromHref, then: fromHref, else: 'https://www.fiestatravel.bg/poleti/rezultati-poleti?flight-type=oneWay&period-type=flexible&adults=1&airports=[_destination_code_]&arrivalAirports={item.code}')}"></a>
                    </f:for>
                    </div>
                </div>
            </f:if>

            <f:if condition="{hasToList}">
                <div class="tp-card-container offer-card-2x2">
                    <div class="active-offers__title">
                        <h2>
                            <f:if condition="{toTitle}">
                                <f:then>{toTitle}</f:then>
                                <f:else>Еднопосочни до [_destination_name_]</f:else>
                            </f:if>
                        </h2>
                    </div>
                    <div class="mdc-card tp-offer-card">
                    <f:for each="{toList}" as="item">
                        <a class='tp'
                           data-type="flight-link"
                           data-title="{f:if(condition: toItem, then: toItem, else: 'От {item.name}')}"
                           data-endpoint="{f:if(condition: toEndpoint, then: toEndpoint, else: 'https://wizatour.fiestatravel.bg/wt/bg_BG/fieFlights/Search/hotels')}"
                           href="{f:if(condition: toHref, then: toHref, else: 'https://www.fiestatravel.bg/poleti/rezultati-poleti?flight-type=oneWay&period-type=flexible&adults=1&airports=[_destination_code_]&arrivalAirports={item.code}')}"></a>
                    </f:for>
                    </div>
                </div>
            </f:if>
            </div>
        </div>
    </f:then>
</f:if>

</html>
