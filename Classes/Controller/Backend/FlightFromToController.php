<?php
namespace Bgs\LandingPages\Controller\Backend;

use Psr\Http\Message\ResponseInterface;
use TYPO3\CMS\Backend\Template\ModuleTemplateFactory;
use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Extbase\Mvc\Controller\ActionController;
use Bgs\LandingPages\Domain\Repository\FlightFromToRepository;
use Bgs\LandingPages\Domain\Model\FlightFromTo;

/**
 * Backend controller for managing Flight From-To data
 */
class FlightFromToController extends ActionController
{
    protected ModuleTemplateFactory $moduleTemplateFactory;
    protected FlightFromToRepository $flightFromToRepository;

    public function __construct(
        ModuleTemplateFactory $moduleTemplateFactory,
        FlightFromToRepository $flightFromToRepository
    ) {
        $this->moduleTemplateFactory = $moduleTemplateFactory;
        $this->flightFromToRepository = $flightFromToRepository;
    }

    /**
     * List all flight from-to records
     */
    public function listAction(): ResponseInterface
    {
        $moduleTemplate = $this->moduleTemplateFactory->create($this->request);
        $moduleTemplate->setTitle('Flight From-To Management');
        
        $records = $this->flightFromToRepository->findAll();
        
        $this->view->assign('records', $records);
        $moduleTemplate->setContent($this->view->render());
        
        return $this->htmlResponse($moduleTemplate->renderContent());
    }

    /**
     * Show form for creating new record
     */
    public function newAction(): ResponseInterface
    {
        $moduleTemplate = $this->moduleTemplateFactory->create($this->request);
        $moduleTemplate->setTitle('Create Flight From-To Record');
        
        $this->view->assign('flightFromTo', new FlightFromTo());
        $moduleTemplate->setContent($this->view->render());
        
        return $this->htmlResponse($moduleTemplate->renderContent());
    }

    /**
     * Create new record
     */
    public function createAction(FlightFromTo $flightFromTo): ResponseInterface
    {
        $this->flightFromToRepository->add($flightFromTo);
        
        $this->addFlashMessage('Flight From-To record created successfully.');
        
        return $this->redirect('list');
    }

    /**
     * Show edit form
     */
    public function editAction(FlightFromTo $flightFromTo): ResponseInterface
    {
        $moduleTemplate = $this->moduleTemplateFactory->create($this->request);
        $moduleTemplate->setTitle('Edit Flight From-To Record');
        
        $this->view->assign('flightFromTo', $flightFromTo);
        $moduleTemplate->setContent($this->view->render());
        
        return $this->htmlResponse($moduleTemplate->renderContent());
    }

    /**
     * Update record
     */
    public function updateAction(FlightFromTo $flightFromTo): ResponseInterface
    {
        $this->flightFromToRepository->update($flightFromTo);
        
        $this->addFlashMessage('Flight From-To record updated successfully.');
        
        return $this->redirect('list');
    }

    /**
     * Delete record
     */
    public function deleteAction(FlightFromTo $flightFromTo): ResponseInterface
    {
        $this->flightFromToRepository->remove($flightFromTo);
        
        $this->addFlashMessage('Flight From-To record deleted successfully.');
        
        return $this->redirect('list');
    }
}
