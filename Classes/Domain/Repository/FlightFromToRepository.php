<?php
namespace Bgs\LandingPages\Domain\Repository;

use TYPO3\CMS\Extbase\Persistence\Repository;
use Bgs\LandingPages\Domain\Model\FlightFromTo;

/**
 * Repository for FlightFromTo model
 */
class FlightFromToRepository extends Repository
{
    /**
     * Set default ordering by sorting field
     */
    public function initializeObject(): void
    {
        $querySettings = $this->createQuery()->getQuerySettings();
        $this->setDefaultOrderings(['sorting' => 'ASC']);
    }
    /**
     * Find FlightFromTo record by content element UID
     *
     * @param int $contentElementUid
     * @return FlightFromTo|null
     */
    public function findByContentElementUid(int $contentElementUid): ?FlightFromTo
    {
        $query = $this->createQuery();
        $query->matching(
            $query->equals('contentElementUid', $contentElementUid)
        );
        
        return $query->execute()->getFirst();
    }
}
