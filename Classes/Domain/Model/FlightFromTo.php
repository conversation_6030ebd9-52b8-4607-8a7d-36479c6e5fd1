<?php
namespace Bgs\LandingPages\Domain\Model;

use TYPO3\CMS\Extbase\DomainObject\AbstractEntity;

/**
 * Flight From-To lists for content elements
 */
class FlightFromTo extends AbstractEntity
{
    protected int $contentElementUid = 0;
    protected string $fromList = '';
    protected string $toList = '';
    protected string $fromTitle = '';
    protected string $fromItem = '';
    protected string $fromEndpoint = '';
    protected string $fromHref = '';
    protected string $toTitle = '';
    protected string $toItem = '';
    protected string $toEndpoint = '';
    protected string $toHref = '';

    public function getContentElementUid(): int
    {
        return $this->contentElementUid;
    }

    public function setContentElementUid(int $contentElementUid): void
    {
        $this->contentElementUid = $contentElementUid;
    }

    public function getFromList(): string
    {
        return $this->fromList;
    }

    public function setFromList(string $fromList): void
    {
        $this->fromList = $fromList;
    }

    public function getToList(): string
    {
        return $this->toList;
    }

    public function setToList(string $toList): void
    {
        $this->toList = $toList;
    }

    public function getFromTitle(): string
    {
        return $this->fromTitle;
    }

    public function setFromTitle(string $fromTitle): void
    {
        $this->fromTitle = $fromTitle;
    }

    public function getFromItem(): string
    {
        return $this->fromItem;
    }

    public function setFromItem(string $fromItem): void
    {
        $this->fromItem = $fromItem;
    }

    public function getFromEndpoint(): string
    {
        return $this->fromEndpoint;
    }

    public function setFromEndpoint(string $fromEndpoint): void
    {
        $this->fromEndpoint = $fromEndpoint;
    }

    public function getFromHref(): string
    {
        return $this->fromHref;
    }

    public function setFromHref(string $fromHref): void
    {
        $this->fromHref = $fromHref;
    }

    public function getToTitle(): string
    {
        return $this->toTitle;
    }

    public function setToTitle(string $toTitle): void
    {
        $this->toTitle = $toTitle;
    }

    public function getToItem(): string
    {
        return $this->toItem;
    }

    public function setToItem(string $toItem): void
    {
        $this->toItem = $toItem;
    }

    public function getToEndpoint(): string
    {
        return $this->toEndpoint;
    }

    public function setToEndpoint(string $toEndpoint): void
    {
        $this->toEndpoint = $toEndpoint;
    }

    public function getToHref(): string
    {
        return $this->toHref;
    }

    public function setToHref(string $toHref): void
    {
        $this->toHref = $toHref;
    }
}
