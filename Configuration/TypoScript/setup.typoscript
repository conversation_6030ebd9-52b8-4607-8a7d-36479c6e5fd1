# Include SEO extension TypoScript (required for XML sitemap functionality)
@import 'EXT:seo/Configuration/TypoScript/XmlSitemap/setup.typoscript'

# XML Sitemap configuration for Flight Routes
# Add our custom flight routes sitemap to the existing configuration
plugin.tx_seo.config.xmlSitemap.sitemaps.landingPages {
    provider = Bgs\LandingPages\XmlSitemap\FlightRoutesXmlSitemapDataProvider
    config {
        # Configuration can be added here if needed in the future
    }
}

# Landing Pages Extension Setup

# Import fluid_styled_content for content element rendering
@import 'EXT:fluid_styled_content/Configuration/TypoScript/setup.typoscript'


# Configuration for Template Pages (doktype 200)
# These pages render normally but are typically not linked in navigation
# They serve as content templates for Landing Pages

# Landing Pages use the site's existing PAGE object configuration
# No custom PAGE object needed - they render as normal TYPO3 pages
# Global configuration for virtual routes and landing pages
# This ensures route data is available for both normal landing pages and virtual routes
page.10.dataProcessing {
    # Add virtual route data processor for all pages
    # This will provide route data when available (virtual routes or landing pages)
    1000 = Bgs\LandingPages\DataProcessing\VirtualRouteDataProcessor
    1000 {
        as = routeData
    }
}




# Plugin configuration for RouteReference list (when used as content element)
plugin.tx_landingpages {
    # Override lib.dynamicContent to handle both virtual routes and normal content
    # This ensures that existing templates work without modification
    lib.dynamicContent = USER
    lib.dynamicContent {
        userFunc = Bgs\LandingPages\UserFunc\VirtualRouteContentRenderer->render
    }

    # Also provide a fallback CONTENT object for lib.dynamicContent
    # This ensures compatibility if the UserFunc approach doesn't work
    lib.dynamicContent.fallback = CONTENT
    lib.dynamicContent.fallback {
        table = tt_content
        select {
            orderBy = sorting
            where = colPos={register:colPos}
            where.insertData = 1
            languageField = sys_language_uid
        }
    }


    view {
        templateRootPaths {
            0 = EXT:landing-pages/Resources/Private/Templates/
            1 = {$plugin.tx_landingpages.view.templateRootPath}
        }
        partialRootPaths {
            0 = EXT:fluid_styled_content/Resources/Private/Partials/
            1 = {$plugin.tx_landingpages.view.partialRootPath}
        }
        layoutRootPaths {
            0 = EXT:landing-pages/Resources/Private/Layouts/
            1 = {$plugin.tx_landingpages.view.layoutRootPath}
        }
    }

    persistence {
        storagePid = {$plugin.tx_landingpages.persistence.storagePid}
    }

    settings {
        # API configuration for flight data
        api {
            baseUrl = https://api.example.com/flights
            apiKey =
            cacheLifetime = 3600
        }
    }

    # Include CSS and JS only for landing pages

}


# Content element configuration for Destination Pairs Menu
tt_content.landingpages_destinationpairsmenu = FLUIDTEMPLATE
tt_content.landingpages_destinationpairsmenu {
    templateName = DestinationPairsMenu
    templateRootPaths {
        0 = EXT:landing-pages/Resources/Private/Templates/ContentElements/
        10 = {$plugin.tx_landingpages.view.templateRootPath}ContentElements/
    }
    partialRootPaths {
        0 = EXT:fluid_styled_content/Resources/Private/Partials/
        10 = {$plugin.tx_landingpages.view.partialRootPath}
    }
    layoutRootPaths {
        0 = EXT:fluid_styled_content/Resources/Private/Layouts/
        10 = {$plugin.tx_landingpages.view.layoutRootPath}
    }
    dataProcessing {
        10 = Bgs\LandingPages\DataProcessing\DestinationPairsMenuProcessor
    }
}

# Content element configuration for FlightFromTo
tt_content.landingpages_flightfromto = FLUIDTEMPLATE
tt_content.landingpages_flightfromto {
    templateName = FlightFromTo
    templateRootPaths {
        0 = EXT:landing-pages/Resources/Private/Templates/ContentElements/
        10 = {$plugin.tx_landingpages.view.templateRootPath}ContentElements/
    }
    partialRootPaths {
        0 = EXT:fluid_styled_content/Resources/Private/Partials/
        10 = {$plugin.tx_landingpages.view.partialRootPath}
    }
    layoutRootPaths {
        0 = EXT:fluid_styled_content/Resources/Private/Layouts/
        10 = {$plugin.tx_landingpages.view.layoutRootPath}
    }
    dataProcessing {
        10 = Bgs\LandingPages\DataProcessing\FlightFromToProcessor
    }
}




[page["doktype"] == 201]
    page {
        includeCSS {
            landingpages = EXT:landing-pages/Resources/Public/CSS/styles.css
        }
        includeJSFooter {
            landingpages = EXT:landing-pages/Resources/Public/JavaScript/main.js
        }
    }
[END]

