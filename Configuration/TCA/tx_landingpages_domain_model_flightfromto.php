<?php
return [
    'ctrl' => [
        'title' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:tx_landingpages_domain_model_flightfromto',
        'label' => 'from_title',
        'label_alt' => 'content_element_uid,from_list,to_list',
        'label_alt_force' => true,
        'tstamp' => 'tstamp',
        'crdate' => 'crdate',
        'delete' => 'deleted',
        'sortby' => 'sorting',
        'searchFields' => 'from_list,to_list,from_title,from_item,to_title,to_item',
        'iconfile' => 'EXT:landing-pages/Resources/Public/Icons/tx_landingpages_domain_model_flightfromto.svg',
        'hideTable' => false,
        'security' => [
            'ignorePageTypeRestriction' => true,
        ],
    ],
    'types' => [
        '1' => ['showitem' => 'content_element_uid, --div--;From List Configuration, from_title, from_item, from_endpoint, from_href, from_list, --div--;To List Configuration, to_title, to_item, to_endpoint, to_href, to_list'],
    ],
    'columns' => [
        'content_element_uid' => [
            'exclude' => true,
            'label' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:tx_landingpages_domain_model_flightfromto.content_element_uid',
            'config' => [
                'type' => 'number',
                'size' => 10,
                'default' => 0,
            ],
        ],
        'from_title' => [
            'exclude' => true,
            'label' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:tx_landingpages_domain_model_flightfromto.from_title',
            'config' => [
                'type' => 'input',
                'size' => 50,
                'max' => 255,
            ],
        ],
        'from_item' => [
            'exclude' => true,
            'label' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:tx_landingpages_domain_model_flightfromto.from_item',
            'config' => [
                'type' => 'input',
                'size' => 50,
                'max' => 255,
                'placeholder' => 'Flight to {item.name} ({item.code})',
            ],
        ],
        'from_endpoint' => [
            'exclude' => true,
            'label' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:tx_landingpages_domain_model_flightfromto.from_endpoint',
            'config' => [
                'type' => 'input',
                'size' => 50,
                'max' => 255,
                'placeholder' => 'https://example.com/search?dest={item.code}',
            ],
        ],
        'from_href' => [
            'exclude' => true,
            'label' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:tx_landingpages_domain_model_flightfromto.from_href',
            'config' => [
                'type' => 'input',
                'size' => 50,
                'max' => 255,
                'placeholder' => 'https://example.com/flights?to={item.code}&from=SOF',
            ],
        ],
        'to_title' => [
            'exclude' => true,
            'label' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:tx_landingpages_domain_model_flightfromto.to_title',
            'config' => [
                'type' => 'input',
                'size' => 50,
                'max' => 255,
            ],
        ],
        'from_list' => [
            'exclude' => true,
            'label' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:tx_landingpages_domain_model_flightfromto.from_list',
            'config' => [
                'type' => 'text',
                'cols' => 50,
                'rows' => 6,
                'placeholder' => "BER - Berlin Brandenburg Airport\nMUC - Munich Airport\nFRA - Frankfurt Airport",
            ],
        ],
        'to_item' => [
            'exclude' => true,
            'label' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:tx_landingpages_domain_model_flightfromto.to_item',
            'config' => [
                'type' => 'input',
                'size' => 50,
                'max' => 255,
            ],
        ],
        'to_endpoint' => [
            'exclude' => true,
            'label' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:tx_landingpages_domain_model_flightfromto.to_endpoint',
            'config' => [
                'type' => 'input',
                'size' => 50,
                'max' => 255,
            ],
        ],
        'to_href' => [
            'exclude' => true,
            'label' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:tx_landingpages_domain_model_flightfromto.to_href',
            'config' => [
                'type' => 'input',
                'size' => 50,
                'max' => 255,
            ],
        ],
        'to_list' => [
            'exclude' => true,
            'label' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:tx_landingpages_domain_model_flightfromto.to_list',
            'config' => [
                'type' => 'text',
                'cols' => 50,
                'rows' => 6,
                'placeholder' => "SOF - Sofia Airport\nATH - Athens Airport\nBCN - Barcelona Airport",
            ],
        ],
    ],
];
